#!/bin/bash

# KickoffScore Project Cleanup Script
# Safely removes temporary files, deployment scripts, and generated archives

set -e

echo "🧹 KickoffScore Project Cleanup"
echo "==============================="
echo "This script will remove temporary files and deployment scripts."
echo "Essential project files will be preserved."
echo ""

# Function to safely remove files/directories
safe_remove() {
    local item="$1"
    if [ -e "$item" ]; then
        echo "  ✅ Removing: $item"
        rm -rf "$item"
    else
        echo "  ⚠️  Not found: $item"
    fi
}

# Function to list files that will be removed
list_files_to_remove() {
    echo "📋 Files and directories that will be REMOVED:"
    echo "=============================================="
    echo ""
    
    echo "🗂️  Deployment Scripts:"
    echo "  ./auto-deploy-elo.sh"
    echo "  ./check-server-status.sh"
    echo "  ./deploy-cluster-safe.sh"
    echo "  ./deploy-elo-mappings.sh"
    echo "  ./deploy-prediction-performance-fix.sh"
    echo "  ./deploy.sh"
    echo "  ./final-fix-502.sh"
    echo "  ./fix-502-error.sh"
    echo "  ./fix-mongodb-and-deploy.sh"
    echo "  ./fix-mongodb-simple.sh"
    echo "  ./fix-performance-issues.sh"
    echo "  ./fix-with-single-instance.sh"
    echo "  ./optimize-for-hetzner-cx22.sh"
    echo "  ./test-improvements.sh"
    echo "  ./verify_deployment.sh"
    echo ""
    
    echo "📦 Compressed Archives:"
    echo "  ./cluster-safe-deploy.tar.gz"
    echo "  ./hetzner-cx22-optimized.tar.gz"
    echo "  ./performance-fix-deploy.tar.gz"
    echo "  ./prediction-performance-fix.tar.gz"
    echo ""
    
    echo "📄 Generated Documentation:"
    echo "  ./Fixed_502_MongoDB_OOM_Error__2025-07-25T09-47-58.md"
    echo "  ./deployment_summary.md"
    echo ""
    
    echo "📁 Empty Directories:"
    echo "  ./scripts/ (empty directory)"
    echo ""
    
    echo "🔍 Optional (will ask for confirmation):"
    echo "  ./dist/ (TypeScript build output - can be regenerated)"
    echo "  ./documentation/endpoints.txt"
    echo "  ./documentation/how-to-save-api.txt"
    echo ""
    
    echo "✅ Files that will be PRESERVED:"
    echo "==============================="
    echo "  ./src/ (source code)"
    echo "  ./package.json"
    echo "  ./package-lock.json"
    echo "  ./tsconfig.json"
    echo "  ./ecosystem.config.js"
    echo "  ./.env"
    echo "  ./node_modules/"
    echo "  ./certs/"
    echo "  ./uploads/ (directory structure)"
    echo "  ./documentation/ELO_INTEGRATION.md"
    echo "  ./documentation/ENHANCED_PREDICTION_SYSTEM_ANALYSIS.md"
    echo "  ./documentation/PROJECT_DOCUMENTATION.md"
    echo ""
}

# Show what will be removed
list_files_to_remove

# Ask for confirmation
read -p "🤔 Do you want to proceed with the cleanup? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cleanup cancelled."
    exit 0
fi

echo ""
echo "🧹 Starting cleanup..."
echo "====================="

# Remove deployment scripts
echo "🗂️  Removing deployment scripts..."
safe_remove "./auto-deploy-elo.sh"
safe_remove "./check-server-status.sh"
safe_remove "./deploy-cluster-safe.sh"
safe_remove "./deploy-elo-mappings.sh"
safe_remove "./deploy-prediction-performance-fix.sh"
safe_remove "./deploy.sh"
safe_remove "./final-fix-502.sh"
safe_remove "./fix-502-error.sh"
safe_remove "./fix-mongodb-and-deploy.sh"
safe_remove "./fix-mongodb-simple.sh"
safe_remove "./fix-performance-issues.sh"
safe_remove "./fix-with-single-instance.sh"
safe_remove "./optimize-for-hetzner-cx22.sh"
safe_remove "./test-improvements.sh"
safe_remove "./verify_deployment.sh"

# Remove compressed archives
echo ""
echo "📦 Removing compressed archives..."
safe_remove "./cluster-safe-deploy.tar.gz"
safe_remove "./hetzner-cx22-optimized.tar.gz"
safe_remove "./performance-fix-deploy.tar.gz"
safe_remove "./prediction-performance-fix.tar.gz"

# Remove generated documentation
echo ""
echo "📄 Removing generated documentation..."
safe_remove "./Fixed_502_MongoDB_OOM_Error__2025-07-25T09-47-58.md"
safe_remove "./deployment_summary.md"

# Remove empty directories
echo ""
echo "📁 Removing empty directories..."
safe_remove "./scripts"

# Ask about optional removals
echo ""
echo "🔍 Optional removals:"
echo "===================="

# Ask about dist directory
if [ -d "./dist" ]; then
    read -p "Remove ./dist/ directory? (can be regenerated with 'npm run build') (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        safe_remove "./dist"
    else
        echo "  ⏭️  Keeping: ./dist/"
    fi
fi

# Ask about optional documentation
if [ -f "./documentation/endpoints.txt" ]; then
    read -p "Remove ./documentation/endpoints.txt? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        safe_remove "./documentation/endpoints.txt"
    else
        echo "  ⏭️  Keeping: ./documentation/endpoints.txt"
    fi
fi

if [ -f "./documentation/how-to-save-api.txt" ]; then
    read -p "Remove ./documentation/how-to-save-api.txt? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        safe_remove "./documentation/how-to-save-api.txt"
    else
        echo "  ⏭️  Keeping: ./documentation/how-to-save-api.txt"
    fi
fi

echo ""
echo "✅ CLEANUP COMPLETED!"
echo "===================="
echo ""
echo "📊 Summary:"
echo "  • Removed temporary deployment scripts"
echo "  • Removed compressed archives"
echo "  • Removed generated documentation"
echo "  • Removed empty directories"
echo "  • Preserved all essential project files"
echo ""
echo "🚀 Your project is now clean and organized!"
echo ""
echo "💡 To rebuild the project after cleanup:"
echo "   npm run build"
echo ""
echo "📁 Current project structure:"
ls -la

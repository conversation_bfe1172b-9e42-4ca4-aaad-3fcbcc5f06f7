<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KickoffScore Live Test</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .match-container {
            display: grid;
            gap: 15px;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        }
        .match-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }
        .match-card:hover {
            transform: translateY(-2px);
        }
        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .league-info {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }
        .match-status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status-live {
            background: #ff4757;
            color: white;
            animation: pulse 2s infinite;
        }
        .status-finished {
            background: #2ed573;
            color: white;
        }
        .status-scheduled {
            background: #ffa502;
            color: white;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .teams {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
        }
        .team {
            display: flex;
            align-items: center;
            flex: 1;
        }
        .team.away {
            justify-content: flex-end;
            text-align: right;
        }
        .team-logo {
            width: 30px;
            height: 30px;
            margin: 0 10px;
            border-radius: 50%;
        }
        .team-name {
            font-weight: bold;
            font-size: 14px;
        }
        .score {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0 20px;
        }
        .match-time {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 10px;
        }
        .events {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .event {
            display: flex;
            align-items: center;
            margin: 5px 0;
            font-size: 12px;
        }
        .event-time {
            background: #667eea;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 8px;
            min-width: 25px;
            text-align: center;
        }
        .event-goal {
            color: #27ae60;
            font-weight: bold;
        }
        .event-card {
            color: #e74c3c;
            font-weight: bold;
        }
        .no-matches {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏆 KickoffScore Live Test</h1>
        <p>Real-time football matches with WebSocket updates</p>
    </div>

    <div id="connectionStatus" class="status disconnected">
        🔴 Connecting to server...
    </div>

    <button class="refresh-btn" onclick="loadLiveMatches()">🔄 Refresh Matches</button>

    <div id="matchContainer" class="match-container">
        <div class="no-matches">Loading live matches...</div>
    </div>

    <script>
        const API_KEY = '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756';
        const API_BASE = 'http://localhost:3000';
        
        let fixtureSocket;
        let matches = {};

        // Initialize the app
        async function init() {
            await loadLiveMatches();
            connectWebSocket();
        }

        // Load live matches from API
        async function loadLiveMatches() {
            try {
                // Use a CORS proxy or direct request
                const response = await fetch(`${API_BASE}/api/fixtures?live=all`, {
                    method: 'GET',
                    headers: {
                        'X-API-Key': API_KEY,
                        'Content-Type': 'application/json'
                    },
                    mode: 'cors' // Explicitly set CORS mode
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const liveMatches = await response.json();
                console.log('Loaded matches:', liveMatches.length);

                // Store matches by ID for easy updates
                matches = {};
                liveMatches.forEach(match => {
                    matches[match._id] = match;
                });

                renderMatches();

            } catch (error) {
                console.error('Error loading matches:', error);

                // Show more detailed error information
                let errorMessage = error.message;
                if (error.message.includes('CORS')) {
                    errorMessage = 'CORS Error: Please serve this file through a web server (not file://) or update CORS settings';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = 'Network Error: Make sure the backend server is running on localhost:3000';
                }

                document.getElementById('matchContainer').innerHTML =
                    `<div class="no-matches">❌ Error loading matches: ${errorMessage}<br><br>
                    <strong>Troubleshooting:</strong><br>
                    1. Make sure backend server is running: <code>npm run dev</code><br>
                    2. Serve this HTML file through a web server, not file://</div>`;
            }
        }

        // Connect to WebSocket for real-time updates
        function connectWebSocket() {
            try {
                fixtureSocket = io(`${API_BASE}/fixtures`);
                
                fixtureSocket.on('connect', () => {
                    console.log('WebSocket connected');
                    updateConnectionStatus(true);
                    
                    // Subscribe to live matches
                    fixtureSocket.emit('subscribe-live');
                });
                
                fixtureSocket.on('disconnect', () => {
                    console.log('WebSocket disconnected');
                    updateConnectionStatus(false);
                });
                
                // Listen for fixture updates
                fixtureSocket.on('fixture-update', (data) => {
                    console.log('Fixture update received:', data);
                    if (matches[data._id]) {
                        matches[data._id] = data;
                        renderMatches();
                    }
                });
                
                // Listen for goals
                fixtureSocket.on('goal-scored', (data) => {
                    console.log('GOAL!', data);
                    // You could add special goal animations here
                });
                
                // Listen for red cards
                fixtureSocket.on('red-card', (data) => {
                    console.log('Red card!', data);
                    // You could add special red card notifications here
                });
                
            } catch (error) {
                console.error('WebSocket connection error:', error);
                updateConnectionStatus(false);
            }
        }

        // Update connection status display
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            if (connected) {
                statusEl.className = 'status connected';
                statusEl.innerHTML = '🟢 Connected to live updates';
            } else {
                statusEl.className = 'status disconnected';
                statusEl.innerHTML = '🔴 Disconnected from live updates';
            }
        }

        // Render all matches
        function renderMatches() {
            const container = document.getElementById('matchContainer');
            const matchList = Object.values(matches);
            
            if (matchList.length === 0) {
                container.innerHTML = '<div class="no-matches">No live matches at the moment</div>';
                return;
            }
            
            container.innerHTML = matchList.map(match => renderMatch(match)).join('');
        }

        // Render individual match
        function renderMatch(match) {
            const status = getMatchStatus(match.fixture.status);
            const events = match.events || [];
            const recentEvents = events.slice(-3); // Show last 3 events
            
            return `
                <div class="match-card" id="match-${match._id}">
                    <div class="match-header">
                        <div class="league-info">
                            ${match.league.name} - ${match.league.round}
                        </div>
                        <div class="match-status ${status.class}">
                            ${status.display}
                        </div>
                    </div>
                    
                    <div class="teams">
                        <div class="team home">
                            <div class="team-name">${match.teams.home.name}</div>
                            ${match.teams.home.logo ? `<img src="${match.teams.home.logo}" class="team-logo" alt="${match.teams.home.name}">` : ''}
                        </div>
                        
                        <div class="score">
                            ${match.goals.home ?? 0} - ${match.goals.away ?? 0}
                        </div>
                        
                        <div class="team away">
                            ${match.teams.away.logo ? `<img src="${match.teams.away.logo}" class="team-logo" alt="${match.teams.away.name}">` : ''}
                            <div class="team-name">${match.teams.away.name}</div>
                        </div>
                    </div>
                    
                    <div class="match-time">
                        ${new Date(match.fixture.date).toLocaleString()}
                        ${match.fixture.status.elapsed ? `(${match.fixture.status.elapsed}')` : ''}
                    </div>
                    
                    ${recentEvents.length > 0 ? `
                        <div class="events">
                            ${recentEvents.map(event => `
                                <div class="event">
                                    <span class="event-time">${event.time.elapsed}'</span>
                                    <span class="event-${event.type.toLowerCase()}">
                                        ${event.type === 'Goal' ? '⚽' : event.type === 'Card' ? '🟨' : ''}
                                        ${event.player.name || 'Unknown'} (${event.team.name})
                                        ${event.detail ? `- ${event.detail}` : ''}
                                    </span>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Get match status with styling
        function getMatchStatus(status) {
            const liveStatuses = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'];
            const finishedStatuses = ['FT', 'AET', 'PEN'];
            
            if (liveStatuses.includes(status.short)) {
                return { display: status.long, class: 'status-live' };
            } else if (finishedStatuses.includes(status.short)) {
                return { display: status.long, class: 'status-finished' };
            } else {
                return { display: status.long, class: 'status-scheduled' };
            }
        }

        // Start the app
        init();
    </script>
</body>
</html>

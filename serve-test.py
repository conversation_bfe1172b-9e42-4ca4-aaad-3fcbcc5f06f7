#!/usr/bin/env python3
"""
Simple HTTP server to serve the live-score-test.html file
This avoids CORS issues when opening the file directly in browser
"""

import http.server
import socketserver
import webbrowser
import os
import sys

PORT = 8080

class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, X-API-Key')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def main():
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Check if the HTML file exists
    if not os.path.exists('live-score-test.html'):
        print("❌ Error: live-score-test.html not found in current directory")
        sys.exit(1)
    
    try:
        with socketserver.TCPServer(("", PORT), CORSRequestHandler) as httpd:
            print(f"🚀 Starting HTTP server on port {PORT}")
            print(f"📱 Open your browser and go to: http://localhost:{PORT}/live-score-test.html")
            print(f"🛑 Press Ctrl+C to stop the server")
            
            # Try to open the browser automatically
            try:
                webbrowser.open(f'http://localhost:{PORT}/live-score-test.html')
                print("🌐 Browser should open automatically...")
            except:
                print("⚠️  Could not open browser automatically")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Error: Port {PORT} is already in use")
            print(f"💡 Try using a different port or stop other services using port {PORT}")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()

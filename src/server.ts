import dotenv from 'dotenv';
dotenv.config(); // Load environment variables from .env file

import express, { Request, Response } from 'express';
import http from 'http';
import path from 'path';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import connectDB from './config/database'; // Import DB connection function
import connectRedis from './config/redis'; // Import Redis connection function
import { createIndexes } from './config/indexes'; // Import index creation function
import './jobs/scheduler'; // Import to initialize cron jobs
import cluster from 'cluster';
import config from './config'; // Import config
import { apiKeyAuth } from './middleware/apiKey'; // Import API key middleware
import countryRoutes from './routes/countryRoutes';
import leagueSeasonRoutes from './routes/leagueSeasonRoutes';
import leagueRoutes from './routes/leagueRoutes';
import teamRoutes from './routes/teamRoutes';
import teamStatisticRoutes from './routes/teamStatisticRoutes';
import teamFormRoutes from './routes/teamFormRoutes';
import venueRoutes from './routes/venueRoutes';
import standingRoutes from './routes/standingRoutes';
import fixtureRoundRoutes from './routes/fixtureRoundRoutes';
import fixtureRoutes from './routes/fixtureRoutes';
import leagueFixturesRoutes from './routes/leagueFixturesRoutes';
import fixtureVoteRoutes from './routes/fixtureVoteRoutes';
import messageRoutes from './routes/messageRoutes';
import predictionRoutes from './routes/predictionRoutes';
import enhancedPredictionRoutes from './routes/enhancedPredictionRoutes';
import injuryRoutes from './routes/injuryRoutes';
import playerRoutes from './routes/playerRoutes';
import coachRoutes from './routes/coachRoutes';
import transferRoutes from './routes/transferRoutes';
import sidelinedRoutes from './routes/sidelinedRoutes';
import oddsRoutes from './routes/oddsRoutes';
import userRoutes from './routes/userRoutes';
import adminRoutes from './routes/adminRoutes';
import notificationRoutes from './routes/notificationRoutes';
import timezoneRoutes from './routes/timezoneRoutes';
import statusRoutes from './routes/statusRoutes';
import eloRoutes from './routes/eloRoutes';
import { setupChatService } from './services/chatService';
import { setupFixtureSocketService } from './services/fixtureSocketService';
import { initializeKnownPlayerMappings } from './services/playerMappingService';
import { initializeEloJobs } from './jobs/eloJobs';

const app = express(); // Re-add app declaration
const port = config.server.port; // Re-add port declaration

// Basic middleware to parse JSON
app.use(express.json());

// Enable CORS for all routes
app.use(cors({
  origin: [
    'https://kickoffpredictions.com',
    'https://www.kickoffpredictions.com',
    'http://localhost:3000' // For local development
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'x-auth-token', 'X-API-Key']
}));

// Serve static files from the uploads directory
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Simple health check route (accessible without API key)
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).send('OK');
});

// Root route for testing (accessible without API key)
app.get('/', (_req: Request, res: Response) => {
  res.send('KickoffScore Backend is running!');
});

// Apply API Key authentication to all API routes
app.use('/api', apiKeyAuth);

// --- API Routes ---
app.use('/api/countries', countryRoutes);
app.use('/api/leagues/seasons', leagueSeasonRoutes);
app.use('/api/leagues', leagueRoutes);
app.use('/api/teams/statistics', teamStatisticRoutes);
app.use('/api/teams/form', teamFormRoutes);
app.use('/api/teams', teamRoutes);
app.use('/api/venues', venueRoutes);
app.use('/api/standings', standingRoutes);
app.use('/api/fixtures/rounds', fixtureRoundRoutes);
app.use('/api/fixtures/votes', fixtureVoteRoutes);
app.use('/api/fixtures', fixtureRoutes);
app.use('/api/fixtures', messageRoutes); // This will handle /api/fixtures/:fixtureId/messages
app.use('/api/league-fixtures', leagueFixturesRoutes); // New route for league-based fixture pagination
app.use('/api/predictions', predictionRoutes);
app.use('/api/enhanced-predictions', enhancedPredictionRoutes);
app.use('/api/injuries', injuryRoutes);
app.use('/api/players', playerRoutes);
app.use('/api/coaches', coachRoutes);
app.use('/api/transfers', transferRoutes);
app.use('/api/sidelined', sidelinedRoutes);
app.use('/api/odds', oddsRoutes);
app.use('/api/users', userRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/timezones', timezoneRoutes);
app.use('/api/status', statusRoutes);
app.use('/api/elo', eloRoutes);
// Add other routers here

// Create HTTP server and integrate Socket.IO
const server = http.createServer(app);

// Setup chat service with Socket.IO
let io: SocketIOServer;

// Function to get the Socket.IO instance
export function getSocketIO(): SocketIOServer | null {
  return io || null;
}

// Function to start the server
async function startServer() {
  try {
    await connectDB(); // Connect to MongoDB
    connectRedis(); // Initialize Redis connection (ioredis handles async connection/reconnection)

    // Only initialize indexes and jobs on the master process or first worker
    const isMainWorker = !cluster.isWorker || cluster.worker?.id === 1;

    if (isMainWorker) {
      await createIndexes(); // Create database indexes
      // Initialize known player ID mappings
      await initializeKnownPlayerMappings();

      // Scheduler is now imported at the top level and initializes automatically
      console.log('✅ Job scheduler imported and initialized at startup');
    }

    // Start the server
    server.listen(port, () => {
      console.log(`Server listening on port ${port} (Worker ${cluster.worker?.id || 'main'})`);

      // Initialize Socket.IO only on main worker to avoid conflicts
      if (isMainWorker) {
        // Initialize a single Socket.IO instance with consistent CORS configuration
        io = new SocketIOServer(server, {
          cors: {
            origin: [
              'https://kickoffpredictions.com',
              'https://www.kickoffpredictions.com',
              'http://localhost:3000' // For local development
            ],
            methods: ['GET', 'POST'],
            credentials: true
          }
        });

        // Setup chat service with the existing io instance
        setupChatService(io);

        // Setup fixture socket service with the existing io instance
        setupFixtureSocketService(io);

        // Start the chat jobs
        import('./jobs/chatJobs').then(chatJobs => {
          chatJobs.chatManagementJob.start();
          console.log('Chat jobs started on main worker');
        });

        // Initialize event detection service
        import('./services/eventDetectionService').then(eventDetection => {
          console.log('Event detection service initialized on main worker');
        });

        // Initialize ELO rating jobs
        initializeEloJobs();
        console.log('ELO jobs initialized on main worker');
      }
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

startServer(); // Call the async function to start the server

// Basic error handling (optional but recommended)
app.use((err: Error, _req: Request, res: Response, _next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).send('Something broke!');
});

export { app, io, server }; // Export for potential testing or extension

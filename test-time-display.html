<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Time Display Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .status { padding: 4px 8px; border-radius: 4px; font-weight: bold; }
        .status-live { background: #ff4757; color: white; }
        .status-finished { background: #2ed573; color: white; }
        .status-scheduled { background: #ffa502; color: white; }
    </style>
</head>
<body>
    <h1>Live Time Display Test</h1>
    
    <div id="test-results"></div>
    
    <script>
        // Test function from the main app
        function getMatchStatus(status) {
            const liveStatuses = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'];
            const finishedStatuses = ['FT', 'AET', 'PEN'];
            
            if (liveStatuses.includes(status.short)) {
                let displayText = '';
                
                // Show live time for active matches
                if (status.elapsed !== null && status.elapsed !== undefined) {
                    if (status.short === '1H') {
                        displayText = `${status.elapsed}'`;
                    } else if (status.short === '2H') {
                        displayText = `${status.elapsed}'`;
                    } else if (status.short === 'HT') {
                        displayText = 'HT';
                    } else if (status.short === 'ET') {
                        displayText = `${status.elapsed}' ET`;
                    } else if (status.short === 'BT') {
                        displayText = 'Break';
                    } else if (status.short === 'P') {
                        displayText = 'Penalties';
                    } else {
                        displayText = `${status.elapsed}'`;
                    }
                } else {
                    displayText = status.long;
                }
                
                return { display: displayText, class: 'status-live' };
            } else if (finishedStatuses.includes(status.short)) {
                return { display: status.long, class: 'status-finished' };
            } else {
                return { display: status.long, class: 'status-scheduled' };
            }
        }
        
        // Test cases
        const testCases = [
            { short: '1H', long: 'First Half', elapsed: 23 },
            { short: '2H', long: 'Second Half', elapsed: 67 },
            { short: 'HT', long: 'Halftime', elapsed: 45 },
            { short: 'ET', long: 'Extra Time', elapsed: 105 },
            { short: 'FT', long: 'Match Finished', elapsed: 90 },
            { short: 'NS', long: 'Not Started', elapsed: null }
        ];
        
        // Render test results
        const container = document.getElementById('test-results');
        testCases.forEach(testCase => {
            const result = getMatchStatus(testCase);
            container.innerHTML += `
                <div class="test-case">
                    <strong>Input:</strong> ${testCase.short} (${testCase.long}) - Elapsed: ${testCase.elapsed}<br>
                    <strong>Output:</strong> <span class="status ${result.class}">${result.display}</span>
                </div>
            `;
        });
    </script>
</body>
</html>
